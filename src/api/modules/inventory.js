import http from '@/utils/request'
export default {
  apiBackgroundDrpInventoryInfoMaterailClassificationStatisticsRecordPost (params) {
    return http.post('/api/background_drp/inventory_info/materail_classification_statistics_record', params)
  },
  apiBackgroundDrpInventoryInfoMaterailClassificationStatisticsRecordExportPost (params) {
    return http.post('/api/background_drp/inventory_info/materail_classification_statistics_record_export', params)
  },
  apiBackgroundDrpInventoryInfoFixCostDetailPost (params) {
    return http.post('/api/background_drp/inventory_info/fix_cost_detail', params)
  },
  apiBackgroundDrpInventoryInfoFixCostDetailExportPost (params) {
    return http.post('/api/background_drp/inventory_info/fix_cost_detail_export', params)
  },
  apiBackgroundDrpInventoryInfoRemarkAddPost (params) {
    return http.post('/api/background_drp/inventory_info/remark_add', params)
  }
}
