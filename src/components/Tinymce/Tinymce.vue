<template>
  <div>
    <vue-tinymce
      :key="tinymceFlag"
      v-model="content"
      :setting="setting"
      @change="change"
      :setup="setup"
    />
  </div>
</template>
<script>
export default {
  name: 'tinymce',
  props: {
    value: {
      type: String,
      default: ""
    },
    disabled: {
      type: Boolean,
      default: false
    },
    itemkey: {
      // 多个富文本用于区分
      type: String,
      default: ''
    },
    custom: {
      // 添加自定义菜单按钮
      type: String,
      default: ''
    },
    height: {
      type: Number,
      default: 500
    },
    listener: String, // 监听的事件，eg: 'input blur focus undo redo execCommand'，多个时使用空格隔开
    customHandle: Function
  },
  activated() {
    this.tinymceFlag++ // 组件缓存的时候用于再次加载，不然有些时候再次加载会出现只显示一个textarea的问题
  },
  data() {
    let _this = this
    return {
      tinymceFlag: 1,
      content: this.content,
      setting: {
        readonly: this.disabled,
        menubar: true, // 上面的菜单隐藏
        selector: `#Editor${_this.itemkey}`, // 多个富文本的时候加上itemkey用于区分
        toolbar: `insertfile undo redo
          | charmap bold italic underline strikethrough subscript  superscript
          | fontsizeselect
          | inserttable table tableprops deletetable cell row
          | alignleft aligncenter alignright alignjustify
          | bullist numlist outdent indent
          | ${_this.custom} image
          `,
        plugins: 'table charmap  preview image link',
        language_url: '/tinymce/zh_CN.js', // 配置中文的路径
        language: 'zh_CN', // 本地化设置
        statusbar: false, // 隐藏最底部的状态栏
        convert_urls: false, // 禁止转换 URL
        // relative_urls: true, // 如果该选项设为true，所有通过MCFileManager返回的URL都会与知道的document_base_url相关。如果设为false，所有URL会被转化成绝对URL，默认为true。
        // document_base_url: 'http://*************/',
        // remove_script_host: false, // 如果此选项设置为true，则document_base_url的协议和主机被排除在相对链路之外。
        height: this.height,
        min_height: this.height,
        max_height: this.height,
        image_advtab: true,
        image_dimensions: false,
        content_style: 'img{max-width:100%;height:auto;}p {margin: 0px;}',
        fontsize_formats: '11px 12px 14px 16px 18px 24px 36px 48px',
        // 图片异步上传处理函数
        images_upload_handler: (blobInfo, success, failure) => {
          // 本地base64图片
          // var reader = new FileReader()
          // reader.onload = function(e) {
          //   // reader.result就是转换成的base64
          //   success(reader.result)
          // }
          // reader.readAsDataURL(blobInfo.blob())

          // 把图片上传到服务器
          var formData = new FormData();
          // 添加下文件
          formData.append('file', blobInfo.blob(), blobInfo.filename());
          // 文件上传的目录
          formData.append('prefix', 'upload');
          // 文件名
          formData.append('key', new Date().getTime() + Math.floor(Math.random() * 150) + blobInfo.filename());
          // _this.$api.uploadScenicFace这个是我调用后台图片上传接口的函数
          _this.$apis.apiBackgroundFileUploadPost(formData).then(res => {
            // 图片上传成功以后调用success,图片就可以在富文本编辑器中显示了
            success(res.data.public_url);
          }).catch(error => {
            failure(error)
          });
        }
      }
    }
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler(newValue, oldValue) {
        // 这里是从列表页编辑时做的内容注入，没有需要可以不写
        if (newValue === undefined) {
          this.content = ''
        } else {
          this.content = newValue
        }
      }
    },
    content: {
      deep: true,
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.$emit("input", newValue);
          console.log(newValue)
        }
      }
    }
  },
  mounted() {},
  methods: {
    change(editor) {
      // 这里只有输入框改变的时候才会触发，比如自定义模板选择的时候就触发不了
    },
    setup(editor) {
      if (this.listener) {
        editor.on(this.listener, e => {
          if (this.customHandle) {
            this.customHandle(e)
          }
        })
      }
      // editor.on('input blur undo redo execCommand', e => {
      //   // 多个触发事件获取最新值
      //   var msg = _this.Editortext.toString() // 获取带html的值
      //   if (_this.itemkey !== undefined && _this.itemkey !== '') {
      //     // 多个富文本时返回值给父组件
      //     _this.$emit('message', {
      //       key: _this.itemkey,
      //       msg: msg
      //     })
      //   } else {
      //     // 单个富文本返回值给父组件
      //     _this.$emit('message', msg)
      //   }
      // })
      // 添加自定义的菜单按钮
      // if (_this.custom.indexOf('menuDateButton') !== -1) {
      //   editor.ui.registry.addMenuButton('menuDateButton', {
      //     // 添加菜单按钮
      //     text: '公式模板',
      //     fetch: function(callback) {
      //       var items = []
      //       let formula = [
      //         { name: '公式1', code: '1' },
      //         { name: '公式2', code: '2' },
      //         { name: '公式3', code: '3' }
      //       ]
      //       formula.map(resitem => {
      //         items.push({
      //           type: 'menuitem',
      //           text: resitem.name,
      //           onAction: function(_) {
      //             editor.insertContent(resitem.name)
      //             editor.input()
      //           }
      //         })
      //       })
      //       callback(items)
      //     }
      //   })
      // }
    }
  }
}
</script>
<style lang="scss">
// 添加下弹窗高度，用于解决抽屉弹窗中嵌套弹窗的层级问题
.tox-tinymce-aux{
  z-index: 2002 !important;
}
</style>
