<template>
  <div class="refresh-tool-wrapper">
    <slot name="title">
      <div class="page-title">
        {{ title ? title : this.generateTitle($route.meta.title) }}
      </div>
    </slot>
    <img v-if="showRefresh" @click="refreshHandler" class="refresh" src="@/assets/img/refresh.png" alt="">
  </div>
</template>

<script>
import { generateTitle } from "@/utils/i18n"
export default {
  name: 'refreshTool',
  props: {
    // 自定义pagetitle
    title: {
      type: String,
      default: ''
    },
    // 是否显示刷新按钮
    showRefresh: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
    }
  },
  computed: {
    // selectval: {
    //   get() {
    //     return this.select
    //   },
    //   set(val) {
    //     this.$emit('update:select', val)
    //   }
    // }
  },
  watch: {
    // $route: {
    //   handler: function(route) {
    //     const query = route.query
    //   },
    //   immediate: true
    // }
  },
  created() {},
  mounted() {},
  updated() {
  },
  methods: {
    generateTitle,
    refreshHandler() {
      this.$emit("refreshPage");
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

.refresh-tool-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 0;
  background-color: transparent;
  .page-title{
    font-size: 20px;
    color: #23282d;
  }
  .refresh{
    cursor: pointer;
  }
}
</style>
