<template>
  <el-upload
    class="file-upload"
    ref="fileUpload"
    :action="serverUrl"
    :file-list="fileData"
    :data="uploadParams"
    :auto-upload="autoUpload"
    :on-remove="handleRemoveFile"
    :on-success="uploadSuccess"
    :before-upload="beforeUploadHandler"
    :limit='limit'
    v-bind="$attrs"
    :headers="headersOpts"
    :on-error="uploadError"
    :disabled="isLoading"
    >
    <slot :loading="isLoading">
      <el-button :loading="isLoading" class="ps-origin-btn" size="small" type="primary">{{$t('button.upload_file')}}</el-button>
    </slot>
  </el-upload>
</template>

<script>
import { getToken, deepClone } from '@/utils'
export default {
  name: 'fileUpload',
  inheritAttrs: false,
  props: {
    type: {
      // 上传类型
      type: String,
      default: ''
    },
    fileList: {
      type: Array,
      default() {
        return []
      }
    },
    limit: { // 现在数量
      type: Number,
      default: 1
    },
    prefix: { // 上传文件前缀
      type: String,
      default: ''
    },
    rename: { // 重命名
      type: Boolean,
      default: true
    },
    autoUpload: { // 自动上传文件
      type: Boolean,
      default: true
    },
    beforeUpload: Function
  },
  // computed: {
  //   fileData: {
  //     get() {
  //       return this.fileLists
  //     },
  //     set(val) {
  //       this.$emit('update:select', val)
  //     }
  //   }
  // },
  watch: {
    fileList(val, old) {
      if (!this.fileData.length) { // 当没数据时赋值
        console.log(11111, val)
        this.fileData = deepClone(this.fileList)
      }
    }
  },
  data() {
    return {
      isLoading: false,
      serverUrl: '/api/background/file/upload',
      uploadParams: {},
      headersOpts: {
        TOKEN: getToken()
      },
      fileData: []
    }
  },
  created() {
    if (this.prefix) {
      this.uploadParams.prefix = this.prefix
    }
    // 如果是测试环境
    if (this.$store.getters.baseUrl) {
      this.serverUrl = this.$store.getters.baseUrl + this.serverUrl
    }
    // 初始化下
    this.fileData = deepClone(this.fileList)
    // this.getUploadToken()
  },
  mounted() {},
  methods: {
    // 上传文件
    async getUploadToken() {
      const res = await this.$apis.getUploadToken({
        prefix: this.filePrefix
      });
      if (res.code === 0) {
        this.serverUrl = res.data.host;
        this.uploadParams = {
          prefix: res.data.prefix,
          policy: res.data.policy,
          OSSAccessKeyId: res.data.accessid,
          signature: res.data.signature,
          callback: res.data.callback,
          success_action_status: "200"
        };
      } else {
        this.$message.error(res.msg);
      }
    },
    handleRemoveFile(file, fileList) {
      this.fileData.map((item, index) => {
        if (item.name === file.name) {
          this.fileData.splice(index, 1)
        }
      })
      fileList.map((item, index) => {
        if (item.name === file.name) {
          fileList.splice(index, 1)
        }
      })
      this.$emit('fileLists', this.fileData)
    },
    uploadSuccess(res, file, fileList) {
      this.isLoading = false
      if (res.code === 0) {
        // 如果是可以一次上传多个文件时需要在请求都完成时才去操作file-list的数据，不然会中断后续请求的响应处理的
        if (fileList.every(it => it.status === 'success')) {
          fileList.map(item => {
            item.response && this.fileData.push({
              url: item.response.data.public_url,
              name: file.name
            })
          })
          setTimeout(() => {
            this.$emit('fileLists', deepClone(this.fileData))
          })
        }
      } else {
        this.$message.error(res.msg)
        this.handleRemoveFile(file, fileList)
        this.$emit('uploadError', false)
      }
    },
    // 重新设置fileList
    setFileData(fileLists) {
      this.fileData = deepClone(fileLists)
    },
    // 删除其中的数据
    spliceFileData(uid) {
      const index = this.fileData.findIndex(v => v.uid === uid)
      if (index !== -1) {
        this.fileData.splice(index, 1)
      }
    },
    // 清空
    clearHandle() {
      this.$refs.fileUpload.clearFiles()
      this.fileData = []
    },
    beforeUploadHandler(file) {
      if (this.rename) {
        let name = new Date().getTime() + Math.floor(Math.random() * 150) + this.get_suffix(file.name)
        this.uploadParams.key = this.uploadParams.prefix ? (this.uploadParams.prefix + name) : name
      } else {
        this.uploadParams.key = file.name;
      }
      if (this.beforeUpload) {
        const status = this.beforeUpload(file)
        if (status) {
          this.isLoading = true
        }
        return status
      }
    },
    // 获取后缀
    get_suffix(filename) {
      let pos = filename.lastIndexOf(".");
      let suffix = "";
      if (pos !== -1) {
        suffix = filename.substring(pos);
      }
      return suffix;
    },
    // 上传失败
    uploadError(error) {
      console.log("error", error);
      this.$emit('uploadError', false)
    },
    // 手动上传文件
    submitHandle() {
      this.$refs.fileUpload.submit()
    }
  }
}
</script>

<style lang="scss" scoped>
.file-upload{
  display: inline-block;
}
</style>
